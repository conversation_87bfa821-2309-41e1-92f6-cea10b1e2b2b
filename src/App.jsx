import React, { createContext, useEffect, useState } from "react";
import { useRoutes } from "react-router-dom";
import { HelmetProvider } from "react-helmet-async";
import { publicRoute, userRoutes } from "./routes.jsx";
import siteConstant from "./helpers/constant/siteConstant";
import { fetchFromStorage, saveToStorage } from "./helpers/context/storage";
import OneSignalInit from "./OneSignalInit";
import theme from "./views/components/Planner/theme.jsx";

import messagesEn from "./helpers/locales/en.json";
import { SocketContext, socket } from "./helpers/context/socket";
import { BrandProvider } from "./helpers/context/BrandContext";
import { MultiAccountProvider } from "./helpers/context/MultiAccountContext";
import { PermissionProvider } from "./helpers/context/PermissionContext";
import { ThemeProvider } from "@material-tailwind/react";

// const originalError = console.error;
// console.error = (...args) => {
//   if (
//     args[0] &&
//     typeof args[0] === "string" &&
//     args[0].includes("ResizeObserver loop completed")
//   ) {
//     return;
//   }
//   originalError(...args);
// };

const Context = createContext();

const App = () => {
  const localAuth = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
  const viewRoutes = localAuth ? userRoutes : publicRoute;
  const routing = useRoutes(viewRoutes);

  // const viewRoutes = userRoutes;
  // const viewRoutes = localAuth
  //   ? localAuth?.is_admin
  //     ? [...userRoutes, ...adminRoutes]
  //     : userRoutes
  //   : publicRoute;
  // const routing = useRoutes(viewRoutes);

  const menuMessages = {
    en: { ...messagesEn },
  };

  const [locale, setLocale] = useState("en");
  const [messages, setMessages] = useState(menuMessages["en"]);
  const [socialIcons, setSocialIcons] = useState(siteConstant?.SOCIAL_ICONS);
  const [channels, setChanels] = useState(siteConstant?.CHANNEL_LIST);
  const [playerId, setPlayerId] = useState("");

  const switchLanguage = (lang) => {
    saveToStorage(siteConstant?.INDENTIFIERS.acceptlanguage, lang);
    setLocale(lang);
    setMessages(menuMessages[lang]);
  };

  // useEffect(() => {
  //   var defaultLang =
  //     fetchFromStorage(siteConstant?.INDENTIFIERS.acceptlanguage) !== null
  //       ? fetchFromStorage(siteConstant?.INDENTIFIERS.acceptlanguage)
  //       : "en";
  //   setLocale(defaultLang);
  //   setMessages(menuMessages[defaultLang]);
  //   saveToStorage(siteConstant?.INDENTIFIERS.acceptlanguage, defaultLang);
  //   // eslint-disable-next-line
  // }, [locale, fetchFromStorage(siteConstant?.INDENTIFIERS.acceptlanguage)]);

  return (
    <HelmetProvider>
      <BrandProvider>
        <MultiAccountProvider>
          <PermissionProvider>
            <SocketContext.Provider value={socket}>
              <React.Suspense fallback="">
                <Context.Provider
                  value={{
                    locale,
                    switchLanguage,
                    messages,
                    socialIcons,
                    channels,
                    userConnectedChannel: localAuth,
                  }}
                >
                  <OneSignalInit />
                  {routing}
                </Context.Provider>
              </React.Suspense>
            </SocketContext.Provider>
          </PermissionProvider>
        </MultiAccountProvider>
      </BrandProvider>
    </HelmetProvider>
  );
};

export default App;
export { Context as IntlContext };
