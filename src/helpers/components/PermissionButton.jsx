import React from 'react';
import { <PERSON><PERSON>, Toolt<PERSON> } from '@mui/material';
import { usePermissionGuard } from '../hooks/usePermissionGuard';
import { PERMISSION_NAMES } from '../constant/permissions';

/**
 * PermissionButton Component
 * 
 * A button that is only enabled/visible based on user permissions
 */
const PermissionButton = ({
  permission,
  permissions,
  requireAll = false,
  hideWhenDisabled = false,
  showTooltip = true,
  tooltipMessage,
  children,
  disabled = false,
  ...buttonProps
}) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions, loading } = usePermissionGuard();

  // Don't render while loading
  if (loading) {
    return null;
  }

  // Determine if user has required permissions
  let hasAccess = true;
  let requiredPermissionNames = [];

  if (permission) {
    hasAccess = hasPermission(permission);
    requiredPermissionNames = [PERMISSION_NAMES[permission] || permission];
  } else if (permissions && permissions.length > 0) {
    hasAccess = requireAll 
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions);
    requiredPermissionNames = permissions.map(p => PERMISSION_NAMES[p] || p);
  }

  // If user doesn't have access and we should hide the button
  if (!hasAccess && hideWhenDisabled) {
    return null;
  }

  // Determine if button should be disabled
  const isDisabled = disabled || !hasAccess;

  // Create tooltip message
  const getTooltipMessage = () => {
    if (tooltipMessage) return tooltipMessage;
    if (!hasAccess) {
      const permissionText = requiredPermissionNames.join(requireAll ? ' and ' : ' or ');
      return `Requires ${permissionText} permission${requiredPermissionNames.length > 1 ? 's' : ''}`;
    }
    return '';
  };

  const button = (
    <Button
      {...buttonProps}
      disabled={isDisabled}
      sx={{
        ...buttonProps.sx,
        ...(isDisabled && {
          opacity: 0.5,
          cursor: 'not-allowed',
        }),
      }}
    >
      {children}
    </Button>
  );

  // Wrap with tooltip if needed
  if (showTooltip && !hasAccess) {
    return (
      <Tooltip title={getTooltipMessage()} arrow>
        <span>
          {button}
        </span>
      </Tooltip>
    );
  }

  return button;
};

export default PermissionButton;
