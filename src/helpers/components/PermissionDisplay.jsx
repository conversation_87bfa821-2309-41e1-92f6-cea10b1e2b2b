import React from 'react';
import {
  Box,
  Typography,
  Chip,
  Grid,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  CircularProgress,
} from '@mui/material';
import {
  Check as CheckIcon,
  Close as CloseIcon,
  Security as SecurityIcon,
} from '@mui/icons-material';
import { usePermissions } from '../context/PermissionContext';
import { PERMISSION_KEYS, PERMISSION_NAMES, PERMISSION_DESCRIPTIONS } from '../constant/permissions';

/**
 * PermissionDisplay Component
 * 
 * Displays current user permissions in a readable format
 */
const PermissionDisplay = ({ 
  variant = 'detailed', // 'detailed', 'chips', 'list'
  showDescriptions = true,
  title = 'Current Permissions'
}) => {
  const { permissions, loading } = usePermissions();

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" p={3}>
        <CircularProgress size={24} />
        <Typography variant="body2" ml={2}>
          Loading permissions...
        </Typography>
      </Box>
    );
  }

  const renderChipsVariant = () => (
    <Box>
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <SecurityIcon />
        {title}
      </Typography>
      <Box display="flex" flexWrap="wrap" gap={1}>
        {Object.entries(PERMISSION_KEYS).map(([key, permissionKey]) => {
          const hasPermission = permissions[permissionKey];
          const permissionName = PERMISSION_NAMES[permissionKey];
          
          return (
            <Chip
              key={permissionKey}
              label={permissionName}
              color={hasPermission ? 'success' : 'default'}
              variant={hasPermission ? 'filled' : 'outlined'}
              icon={hasPermission ? <CheckIcon /> : <CloseIcon />}
              size="small"
            />
          );
        })}
      </Box>
    </Box>
  );

  const renderListVariant = () => (
    <Box>
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <SecurityIcon />
        {title}
      </Typography>
      <List dense>
        {Object.entries(PERMISSION_KEYS).map(([key, permissionKey]) => {
          const hasPermission = permissions[permissionKey];
          const permissionName = PERMISSION_NAMES[permissionKey];
          const permissionDescription = PERMISSION_DESCRIPTIONS[permissionKey];
          
          return (
            <ListItem key={permissionKey} divider>
              <ListItemIcon>
                {hasPermission ? (
                  <CheckIcon color="success" />
                ) : (
                  <CloseIcon color="error" />
                )}
              </ListItemIcon>
              <ListItemText
                primary={permissionName}
                secondary={showDescriptions ? permissionDescription : null}
                primaryTypographyProps={{
                  color: hasPermission ? 'text.primary' : 'text.secondary',
                }}
              />
            </ListItem>
          );
        })}
      </List>
    </Box>
  );

  const renderDetailedVariant = () => (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <SecurityIcon />
          {title}
        </Typography>
        
        <Grid container spacing={2}>
          {Object.entries(PERMISSION_KEYS).map(([key, permissionKey]) => {
            const hasPermission = permissions[permissionKey];
            const permissionName = PERMISSION_NAMES[permissionKey];
            const permissionDescription = PERMISSION_DESCRIPTIONS[permissionKey];
            
            return (
              <Grid item xs={12} sm={6} md={4} key={permissionKey}>
                <Card 
                  variant="outlined" 
                  sx={{ 
                    height: '100%',
                    borderColor: hasPermission ? 'success.main' : 'grey.300',
                    backgroundColor: hasPermission ? 'success.50' : 'grey.50',
                  }}
                >
                  <CardContent sx={{ p: 2 }}>
                    <Box display="flex" alignItems="center" gap={1} mb={1}>
                      {hasPermission ? (
                        <CheckIcon color="success" fontSize="small" />
                      ) : (
                        <CloseIcon color="error" fontSize="small" />
                      )}
                      <Typography 
                        variant="subtitle2" 
                        color={hasPermission ? 'success.main' : 'text.secondary'}
                        fontWeight="bold"
                      >
                        {permissionName}
                      </Typography>
                    </Box>
                    
                    {showDescriptions && (
                      <Typography 
                        variant="body2" 
                        color="text.secondary"
                        fontSize="0.75rem"
                      >
                        {permissionDescription}
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>
      </CardContent>
    </Card>
  );

  switch (variant) {
    case 'chips':
      return renderChipsVariant();
    case 'list':
      return renderListVariant();
    case 'detailed':
    default:
      return renderDetailedVariant();
  }
};

/**
 * PermissionSummary Component
 * 
 * Shows a quick summary of permissions
 */
export const PermissionSummary = () => {
  const { permissions, loading } = usePermissions();

  if (loading) {
    return (
      <Box display="flex" alignItems="center" gap={1}>
        <CircularProgress size={16} />
        <Typography variant="body2">Loading...</Typography>
      </Box>
    );
  }

  const grantedPermissions = Object.entries(permissions)
    .filter(([key, value]) => value === true)
    .length;
  
  const totalPermissions = Object.keys(permissions).length;

  return (
    <Box display="flex" alignItems="center" gap={1}>
      <SecurityIcon fontSize="small" color="action" />
      <Typography variant="body2" color="text.secondary">
        {grantedPermissions}/{totalPermissions} permissions granted
      </Typography>
    </Box>
  );
};

export default PermissionDisplay;
