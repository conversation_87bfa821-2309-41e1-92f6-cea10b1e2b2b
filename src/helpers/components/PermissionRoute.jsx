import React from 'react';
import { Navigate } from 'react-router-dom';
import { usePermissionGuard } from '../hooks/usePermissionGuard';
import { Box, Typography, Button } from '@mui/material';
import { Lock as LockIcon } from '@mui/icons-material';

/**
 * PermissionRoute Component
 * 
 * A route wrapper that checks permissions before rendering the component
 * Redirects or shows access denied message if user lacks required permissions
 */
const PermissionRoute = ({ 
  children, 
  requiredPermissions = [], 
  requireAll = false, 
  redirectTo = '/dashboard',
  showAccessDenied = true 
}) => {
  const { hasAnyPermission, hasAllPermissions, loading } = usePermissionGuard();

  // Show loading state while permissions are being fetched
  if (loading) {
    return (
      <Box 
        display="flex" 
        justifyContent="center" 
        alignItems="center" 
        minHeight="200px"
      >
        <Typography>Loading permissions...</Typography>
      </Box>
    );
  }

  // If no permissions required, allow access
  if (!requiredPermissions || requiredPermissions.length === 0) {
    return children;
  }

  // Check permissions based on requireAll flag
  const hasAccess = requireAll 
    ? hasAllPermissions(requiredPermissions)
    : hasAnyPermission(requiredPermissions);

  // If user has access, render the component
  if (hasAccess) {
    return children;
  }

  // If showAccessDenied is false, redirect
  if (!showAccessDenied) {
    return <Navigate to={redirectTo} replace />;
  }

  // Show access denied message
  return (
    <AccessDeniedPage 
      requiredPermissions={requiredPermissions}
      redirectTo={redirectTo}
    />
  );
};

/**
 * AccessDeniedPage Component
 * 
 * Displays when user doesn't have required permissions
 */
const AccessDeniedPage = ({ requiredPermissions, redirectTo }) => {
  const handleGoBack = () => {
    window.history.back();
  };

  const handleGoToDashboard = () => {
    window.location.href = redirectTo;
  };

  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      minHeight="60vh"
      textAlign="center"
      px={3}
    >
      <LockIcon 
        sx={{ 
          fontSize: 80, 
          color: 'text.secondary', 
          mb: 2 
        }} 
      />
      
      <Typography 
        variant="h4" 
        component="h1" 
        gutterBottom
        color="text.primary"
      >
        Access Denied
      </Typography>
      
      <Typography 
        variant="body1" 
        color="text.secondary" 
        mb={3}
        maxWidth="500px"
      >
        You don't have the required permissions to access this page. 
        Please contact your administrator if you believe this is an error.
      </Typography>

      {requiredPermissions && requiredPermissions.length > 0 && (
        <Typography 
          variant="body2" 
          color="text.secondary" 
          mb={4}
          sx={{ 
            fontStyle: 'italic',
            opacity: 0.7 
          }}
        >
          Required permissions: {requiredPermissions.join(', ')}
        </Typography>
      )}

      <Box display="flex" gap={2} flexWrap="wrap" justifyContent="center">
        <Button 
          variant="outlined" 
          onClick={handleGoBack}
          sx={{ minWidth: 120 }}
        >
          Go Back
        </Button>
        
        <Button 
          variant="contained" 
          onClick={handleGoToDashboard}
          sx={{ minWidth: 120 }}
        >
          Go to Dashboard
        </Button>
      </Box>
    </Box>
  );
};

export default PermissionRoute;
