/**
 * Permission System Constants
 * 
 * This file defines all permission-related constants used throughout the application
 */

// Permission Keys - These match the backend permission system
export const PERMISSION_KEYS = {
  PRM_POST: 'PRM_POST',
  PRM_MESSAGE: 'PRM_MESSAGE', 
  PRM_ANALYTICS: 'PRM_ANALYTICS',
  PRM_USER_MANAGEMENT: 'PRM_USER_MANAGEMENT',
  PRM_BRAND_MANAGEMENT: 'PRM_BRAND_MANAGEMENT',
  PRM_BLOCK_UNBLOCK: 'PRM_BLOCK_UNBLOCK',
  PRM_FEEDBACK: 'PRM_FEEDBACK',
};

// Permission ID Mapping - Maps API permission IDs to permission keys
export const PERMISSION_ID_MAP = {
  1: PERMISSION_KEYS.PRM_POST,
  2: PERMISSION_KEYS.PRM_MESSAGE,
  3: PERMISSION_KEYS.PRM_ANALYTICS,
  4: PERMISSION_KEYS.PRM_USER_MANAGEMENT,
  5: PERMISSION_KEYS.PRM_BRAND_MANAGEMENT,
  6: PERMISSION_KEYS.PRM_BLOCK_UNBLOCK,
  7: PERMISSION_KEYS.PRM_FEEDBACK,
  8: PERMISSION_KEYS.PRM_POST, // Special case: ID 8 also grants post permissions
};

// Permission Display Names
export const PERMISSION_NAMES = {
  [PERMISSION_KEYS.PRM_POST]: 'Post',
  [PERMISSION_KEYS.PRM_MESSAGE]: 'Messages',
  [PERMISSION_KEYS.PRM_ANALYTICS]: 'Analytics',
  [PERMISSION_KEYS.PRM_USER_MANAGEMENT]: 'User Management',
  [PERMISSION_KEYS.PRM_BRAND_MANAGEMENT]: 'Brand Management',
  [PERMISSION_KEYS.PRM_BLOCK_UNBLOCK]: 'Block & Unblock',
  [PERMISSION_KEYS.PRM_FEEDBACK]: 'Feedback',
};

// Permission Descriptions
export const PERMISSION_DESCRIPTIONS = {
  [PERMISSION_KEYS.PRM_POST]: 'Create and manage posts, stories, and content',
  [PERMISSION_KEYS.PRM_MESSAGE]: 'Access messaging and chat features',
  [PERMISSION_KEYS.PRM_ANALYTICS]: 'View analytics and performance data',
  [PERMISSION_KEYS.PRM_USER_MANAGEMENT]: 'Manage users and team members',
  [PERMISSION_KEYS.PRM_BRAND_MANAGEMENT]: 'Manage brand settings and configuration',
  [PERMISSION_KEYS.PRM_BLOCK_UNBLOCK]: 'Block and unblock users',
  [PERMISSION_KEYS.PRM_FEEDBACK]: 'Access feedback and support features',
};

// Default permissions (all false for security)
export const DEFAULT_PERMISSIONS = {
  [PERMISSION_KEYS.PRM_POST]: false,
  [PERMISSION_KEYS.PRM_MESSAGE]: false,
  [PERMISSION_KEYS.PRM_ANALYTICS]: false,
  [PERMISSION_KEYS.PRM_USER_MANAGEMENT]: false,
  [PERMISSION_KEYS.PRM_BRAND_MANAGEMENT]: false,
  [PERMISSION_KEYS.PRM_BLOCK_UNBLOCK]: false,
  [PERMISSION_KEYS.PRM_FEEDBACK]: false,
};

// Storage keys for permissions
export const PERMISSION_STORAGE_KEY = 'userPermissions';
export const PERMISSION_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds
