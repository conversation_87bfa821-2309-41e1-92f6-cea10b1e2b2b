# Permission System Documentation

This document explains how to use the permission system implemented in your React application.

## Overview

The permission system provides a comprehensive way to control user access to different features and UI elements based on their assigned permissions. It mirrors the functionality from your Dart/Flutter mobile application.

## Architecture

### Core Components

1. **Permission Constants** (`src/helpers/constant/permissions.js`)
   - Defines all permission keys and mappings
   - Maps API permission IDs to permission keys
   - Contains permission names and descriptions

2. **Permission Context** (`src/helpers/context/PermissionContext.jsx`)
   - Manages permission state globally
   - Handles API calls to fetch permissions
   - Provides caching and automatic refresh

3. **Permission Hook** (`src/helpers/hooks/usePermissionGuard.js`)
   - Convenient hook for checking permissions
   - Provides render guards and utility functions

4. **Permission Components**
   - `PermissionRoute.jsx` - Route-level permission guards
   - `PermissionButton.jsx` - Permission-aware buttons
   - `PermissionDisplay.jsx` - Display current permissions

## Permission Types

The system supports these permission types:

- **PRM_POST** - Create and manage posts, stories, content
- **PRM_MESSAGE** - Access messaging and chat features
- **PRM_ANALYTICS** - View analytics and performance data
- **PRM_USER_MANAGEMENT** - Manage users and team members
- **PRM_BRAND_MANAGEMENT** - Manage brand settings
- **PRM_BLOCK_UNBLOCK** - Block and unblock users
- **PRM_FEEDBACK** - Access feedback and support features

## Usage Examples

### 1. Basic Permission Checking

```jsx
import { usePermissionGuard } from '../../../helpers/hooks/usePermissionGuard';
import { PERMISSION_KEYS } from '../../../helpers/constant/permissions';

const MyComponent = () => {
  const { canCreatePost, hasPermission } = usePermissionGuard();

  return (
    <div>
      {canCreatePost() && (
        <button>Create Post</button>
      )}
      
      {hasPermission(PERMISSION_KEYS.PRM_ANALYTICS) && (
        <div>Analytics Content</div>
      )}
    </div>
  );
};
```

### 2. Permission Guards (Declarative)

```jsx
import { usePermissionGuard } from '../../../helpers/hooks/usePermissionGuard';
import { PERMISSION_KEYS } from '../../../helpers/constant/permissions';

const MyComponent = () => {
  const { PermissionGuard, AnyPermissionGuard } = usePermissionGuard();

  return (
    <div>
      {/* Single permission guard */}
      <PermissionGuard 
        permission={PERMISSION_KEYS.PRM_POST}
        fallback={<div>No post permission</div>}
      >
        <button>Create Post</button>
      </PermissionGuard>

      {/* Multiple permissions (any) */}
      <AnyPermissionGuard 
        permissions={[PERMISSION_KEYS.PRM_POST, PERMISSION_KEYS.PRM_MESSAGE]}
        fallback={<div>Need content permissions</div>}
      >
        <div>Content Management</div>
      </AnyPermissionGuard>
    </div>
  );
};
```

### 3. Permission Buttons

```jsx
import PermissionButton from '../../../helpers/components/PermissionButton';
import { PERMISSION_KEYS } from '../../../helpers/constant/permissions';

const MyComponent = () => {
  return (
    <div>
      <PermissionButton
        permission={PERMISSION_KEYS.PRM_POST}
        variant="contained"
        color="primary"
        onClick={() => console.log('Create post')}
      >
        Create Post
      </PermissionButton>

      <PermissionButton
        permissions={[PERMISSION_KEYS.PRM_USER_MANAGEMENT]}
        hideWhenDisabled={true}
        variant="outlined"
      >
        Manage Users (Hidden if no permission)
      </PermissionButton>
    </div>
  );
};
```

### 4. Route Protection

```jsx
import PermissionRoute from '../../../helpers/components/PermissionRoute';
import { PERMISSION_KEYS } from '../../../helpers/constant/permissions';

// In your route configuration
const routes = [
  {
    path: '/analytics',
    element: (
      <PermissionRoute 
        requiredPermissions={[PERMISSION_KEYS.PRM_ANALYTICS]}
        redirectTo="/dashboard"
      >
        <AnalyticsPage />
      </PermissionRoute>
    )
  },
  {
    path: '/admin',
    element: (
      <PermissionRoute 
        requiredPermissions={[
          PERMISSION_KEYS.PRM_USER_MANAGEMENT,
          PERMISSION_KEYS.PRM_BRAND_MANAGEMENT
        ]}
        requireAll={false} // User needs ANY of these permissions
      >
        <AdminPage />
      </PermissionRoute>
    )
  }
];
```

### 5. Navigation Menu Integration

The sidebar automatically filters navigation items based on permissions:

```jsx
// In sidebar component
const permissionNavItems = [
  {
    id: 4,
    name: "Messages",
    link: "/chat",
    permission: PERMISSION_KEYS.PRM_MESSAGE,
    hasPermission: canAccessMessages(),
    // ... other props
  },
  // ... other items
];

// Filter items based on permissions
const userNavItems = [
  ...baseNavItems,
  ...permissionNavItems.filter(item => item.hasPermission),
  ...additionalNavItems,
];
```

## API Integration

### Permission Fetching

The system automatically fetches permissions when:
- User switches accounts
- Brand changes
- App initializes

### API Endpoints

- `GET /get-user-permissions/` - Fetch user permissions
- Headers: `user` (login user ID), `brand` (brand ID)

### Permission ID Mapping

The API returns permission IDs that are mapped to permission keys:

```javascript
const PERMISSION_ID_MAP = {
  1: 'PRM_POST',
  2: 'PRM_MESSAGE',
  3: 'PRM_ANALYTICS',
  4: 'PRM_USER_MANAGEMENT',
  5: 'PRM_BRAND_MANAGEMENT',
  6: 'PRM_BLOCK_UNBLOCK',
  7: 'PRM_FEEDBACK',
  8: 'PRM_POST', // Special case: ID 8 also grants post permissions
};
```

## Caching and Performance

- Permissions are cached in localStorage for 5 minutes
- Cache is invalidated when user or brand changes
- Automatic refresh on account switching

## Security Features

- **Fail-safe defaults** - All permissions default to `false`
- **API failure handling** - Sets all permissions to `false` on API errors
- **Cache validation** - Ensures cached permissions match current user/brand
- **Real-time updates** - Permissions update immediately when changed

## Best Practices

1. **Always use permission guards** for sensitive UI elements
2. **Combine with route protection** for page-level security
3. **Provide fallback content** when permissions are denied
4. **Use descriptive permission names** in error messages
5. **Test with different permission combinations**

## Troubleshooting

### Common Issues

1. **Permissions not updating**: Check if user/brand context is properly set
2. **API errors**: Verify endpoint URL and headers
3. **Cache issues**: Clear localStorage or check cache expiration
4. **Missing permissions**: Ensure permission keys match backend

### Debug Information

The system provides detailed console logs:
- Permission fetch attempts
- API responses
- Cache operations
- Permission updates

Enable debug mode by checking browser console for `🔐 [Permissions]` logs.
