import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";
import {
  PERMISSION_KEYS,
  PERMISSION_ID_MAP,
  DEFAULT_PERMISSIONS,
  PERMISSION_STORAGE_KEY,
  PERMISSION_CACHE_DURATION,
} from "../constant/permissions";
import { URL } from "../constant/Url";
import apiInstance from "../Axios/axiosINstance";
import { fetchFromStorage, saveToStorage } from "./storage";
import siteConstant from "../constant/siteConstant";
import { useMultiAccount } from "./MultiAccountContext";
import { useBrand } from "./BrandContext";

const PermissionContext = createContext();

export const PermissionProvider = ({ children }) => {
  const [permissions, setPermissions] = useState(DEFAULT_PERMISSIONS);
  const [loading, setLoading] = useState(true);
  const [lastFetchTime, setLastFetchTime] = useState(null);

  const { selectedAccount } = useMultiAccount();
  const { selectedBrand } = useBrand();

  // Debug logging function
  const logPermissions = useCallback((message, data = null) => {
    console.log(`🔐 [Permissions] ${message}`, data || "");
  }, []);

  // Store permissions in localStorage with timestamp
  const storePermissions = useCallback(
    (permissionData) => {
      const permissionCache = {
        permissions: permissionData,
        timestamp: Date.now(),
        userId: selectedAccount?.userId,
        brandId: selectedBrand?.id,
      };

      saveToStorage(PERMISSION_STORAGE_KEY, permissionCache);
      setPermissions(permissionData);
      setLastFetchTime(Date.now());

      logPermissions("Permissions stored:", permissionData);
    },
    [selectedAccount?.userId, selectedBrand?.id, logPermissions]
  );

  // Load permissions from localStorage
  const loadCachedPermissions = useCallback(() => {
    try {
      const cached = fetchFromStorage(PERMISSION_STORAGE_KEY);

      if (!cached) {
        logPermissions("No cached permissions found");
        return false;
      }

      const {
        permissions: cachedPermissions,
        timestamp,
        userId,
        brandId,
      } = cached;
      const now = Date.now();
      const isExpired = now - timestamp > PERMISSION_CACHE_DURATION;
      const isWrongUser = userId !== selectedAccount?.userId;
      const isWrongBrand = brandId !== selectedBrand?.id;

      if (isExpired || isWrongUser || isWrongBrand) {
        logPermissions("Cached permissions invalid:", {
          isExpired,
          isWrongUser,
          isWrongBrand,
          cachedUserId: userId,
          currentUserId: selectedAccount?.userId,
          cachedBrandId: brandId,
          currentBrandId: selectedBrand?.id,
        });
        return false;
      }

      setPermissions(cachedPermissions);
      setLastFetchTime(timestamp);
      logPermissions("Loaded cached permissions:", cachedPermissions);
      return true;
    } catch (error) {
      logPermissions("Error loading cached permissions:", error);
      return false;
    }
  }, [selectedAccount?.userId, selectedBrand?.id, logPermissions]);

  // Map permission IDs from API to permission object
  const mapPermissionIds = useCallback(
    (permissionIds) => {
      const permissionData = { ...DEFAULT_PERMISSIONS };

      // Set all permissions to false first
      Object.keys(permissionData).forEach((key) => {
        permissionData[key] = false;
      });

      // Set permissions to true for IDs present in response
      permissionIds.forEach((permissionId) => {
        const permissionKey = PERMISSION_ID_MAP[permissionId];
        if (permissionKey) {
          permissionData[permissionKey] = true;
        }
      });

      logPermissions("Mapped permission IDs:", {
        permissionIds,
        permissionData,
      });
      return permissionData;
    },
    [logPermissions]
  );

  // Set all permissions to a specific value
  const setAllPermissions = useCallback(
    (value) => {
      const permissionData = {};
      Object.keys(DEFAULT_PERMISSIONS).forEach((key) => {
        permissionData[key] = value;
      });

      storePermissions(permissionData);
      logPermissions(`All permissions set to: ${value}`);
    },
    [storePermissions, logPermissions]
  );

  // Fetch permissions from API
  const fetchUserPermissions = useCallback(
    async (loginUserId, brandId, currentUserId) => {
      try {
        setLoading(true);
        logPermissions("=== PERMISSION HANDLING DEBUG ===");
        logPermissions(
          `Login User ID: ${loginUserId} (type: ${typeof loginUserId})`
        );
        logPermissions(
          `Current User ID: ${currentUserId} (type: ${typeof currentUserId})`
        );
        logPermissions(`Brand ID: ${brandId}`);
        logPermissions(`Are they same user? ${loginUserId === currentUserId}`);
        logPermissions(
          `String comparison: ${String(loginUserId) === String(currentUserId)}`
        );

        // Check if login user is switching back to their own account
        if (loginUserId === currentUserId) {
          logPermissions(
            "Login user switching back to own account - granting all permissions"
          );
          setAllPermissions(true);
          return;
        }

        logPermissions(
          "🔄 User switching to different account - fetching permissions from API"
        );

        // Fetch permissions from API for other users
        const response = await apiInstance.get(URL.GET_USER_PERMISSIONS, {
          headers: {
            user: loginUserId,
            brand: brandId,
          },
        });

        logPermissions("API Response Status:", response.data?.status);
        logPermissions("API Response Data:", response.data?.data);
        logPermissions("API Response Message:", response.data?.message);

        if (response.data?.status && response.data?.data) {
          const permissionData = mapPermissionIds(response.data.data);
          storePermissions(permissionData);
          logPermissions(
            `Permissions stored successfully for user: ${currentUserId}`
          );
        } else {
          // If API fails, set all permissions to false
          setAllPermissions(false);
          logPermissions("❌ API failed - setting all permissions to false");
        }
      } catch (error) {
        logPermissions("Error fetching user permissions:", error);
        // On error, set all permissions to false for security
        setAllPermissions(false);
      } finally {
        setLoading(false);
      }
    },
    [logPermissions, mapPermissionIds, storePermissions, setAllPermissions]
  );

  // Handle user/brand changes
  useEffect(() => {
    if (!selectedAccount || !selectedBrand) {
      logPermissions(
        "No selected account or brand, setting default permissions"
      );
      setPermissions(DEFAULT_PERMISSIONS);
      setLoading(false);
      return;
    }

    // Try to load cached permissions first
    const hasCachedPermissions = loadCachedPermissions();

    if (!hasCachedPermissions) {
      // Get main account info for API calls
      const userData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
      const mainUserId = userData?.userId;

      if (mainUserId && selectedBrand?.id && selectedAccount?.userId) {
        fetchUserPermissions(
          mainUserId,
          selectedBrand.id,
          selectedAccount.userId
        );
      } else {
        logPermissions("Missing required data for permission fetch:", {
          mainUserId,
          brandId: selectedBrand?.id,
          currentUserId: selectedAccount?.userId,
        });
        setAllPermissions(false);
      }
    } else {
      setLoading(false);
    }
  }, [
    selectedAccount,
    selectedBrand,
    loadCachedPermissions,
    fetchUserPermissions,
    setAllPermissions,
    logPermissions,
  ]);

  // Refresh permissions manually
  const refreshPermissions = useCallback(() => {
    if (selectedAccount && selectedBrand) {
      const userData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
      const mainUserId = userData?.userId;

      if (mainUserId) {
        fetchUserPermissions(
          mainUserId,
          selectedBrand.id,
          selectedAccount.userId
        );
      }
    }
  }, [selectedAccount, selectedBrand, fetchUserPermissions]);

  // Clear permissions
  const clearPermissions = useCallback(() => {
    setPermissions(DEFAULT_PERMISSIONS);
    localStorage.removeItem(PERMISSION_STORAGE_KEY);
    logPermissions("Permissions cleared");
  }, [logPermissions]);

  // Debug function for development
  const debugPermissions = useCallback(() => {
    logPermissions("=== CURRENT PERMISSION STATE ===");
    logPermissions("Permissions:", permissions);
    logPermissions("Selected Account:", selectedAccount);
    logPermissions("Selected Brand:", selectedBrand);
    logPermissions("Loading:", loading);

    // Check localStorage cache
    const cacheKey = `${PERMISSION_STORAGE_KEY}_${selectedAccount?.userId}_${selectedBrand?.id}`;
    const cachedData = localStorage.getItem(cacheKey);
    logPermissions("Cache Key:", cacheKey);
    logPermissions(
      "Cached Data:",
      cachedData ? JSON.parse(cachedData) : "No cache"
    );

    logPermissions("=== END DEBUG ===");

    // Make debug function available globally for console access
    window.debugPermissions = debugPermissions;
  }, [permissions, selectedAccount, selectedBrand, loading, logPermissions]);

  // Check if user has specific permission
  const hasPermission = useCallback(
    (permissionKey) => {
      return permissions[permissionKey] === true;
    },
    [permissions]
  );

  // Check if user has any of the specified permissions
  const hasAnyPermission = useCallback(
    (permissionKeys) => {
      return permissionKeys.some((key) => hasPermission(key));
    },
    [hasPermission]
  );

  // Check if user has all of the specified permissions
  const hasAllPermissions = useCallback(
    (permissionKeys) => {
      return permissionKeys.every((key) => hasPermission(key));
    },
    [hasPermission]
  );

  // Make debug function available globally
  useEffect(() => {
    window.debugPermissions = debugPermissions;
  }, [debugPermissions]);

  const value = {
    permissions,
    loading,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    refreshPermissions,
    clearPermissions,
    debugPermissions,
    lastFetchTime,
  };

  return (
    <PermissionContext.Provider value={value}>
      {children}
    </PermissionContext.Provider>
  );
};

export const usePermissions = () => {
  const context = useContext(PermissionContext);
  if (!context) {
    throw new Error("usePermissions must be used within a PermissionProvider");
  }
  return context;
};
