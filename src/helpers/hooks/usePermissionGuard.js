import { usePermissions } from '../context/PermissionContext';
import { PERMISSION_KEYS } from '../constant/permissions';

/**
 * Custom hook for permission-based UI guards
 * 
 * This hook provides convenient methods to check permissions and conditionally render UI elements
 */
export const usePermissionGuard = () => {
  const { permissions, hasPermission, hasAnyPermission, hasAllPermissions, loading } = usePermissions();

  // Specific permission checkers for common use cases
  const canCreatePost = () => hasPermission(PERMISSION_KEYS.PRM_POST);
  const canAccessMessages = () => hasPermission(PERMISSION_KEYS.PRM_MESSAGE);
  const canViewAnalytics = () => hasPermission(PERMISSION_KEYS.PRM_ANALYTICS);
  const canManageUsers = () => hasPermission(PERMISSION_KEYS.PRM_USER_MANAGEMENT);
  const canManageBrands = () => hasPermission(PERMISSION_KEYS.PRM_BRAND_MANAGEMENT);
  const canBlockUnblock = () => hasPermission(PERMISSION_KEYS.PRM_BLOCK_UNBLOCK);
  const canAccessFeedback = () => hasPermission(PERMISSION_KEYS.PRM_FEEDBACK);

  // Combined permission checkers
  const canAccessContent = () => hasAnyPermission([
    PERMISSION_KEYS.PRM_POST,
    PERMISSION_KEYS.PRM_MESSAGE,
  ]);

  const canAccessAdminFeatures = () => hasAnyPermission([
    PERMISSION_KEYS.PRM_USER_MANAGEMENT,
    PERMISSION_KEYS.PRM_BRAND_MANAGEMENT,
    PERMISSION_KEYS.PRM_BLOCK_UNBLOCK,
  ]);

  const isFullAdmin = () => hasAllPermissions([
    PERMISSION_KEYS.PRM_POST,
    PERMISSION_KEYS.PRM_MESSAGE,
    PERMISSION_KEYS.PRM_ANALYTICS,
    PERMISSION_KEYS.PRM_USER_MANAGEMENT,
    PERMISSION_KEYS.PRM_BRAND_MANAGEMENT,
    PERMISSION_KEYS.PRM_BLOCK_UNBLOCK,
    PERMISSION_KEYS.PRM_FEEDBACK,
  ]);

  // Render guards - components that conditionally render children based on permissions
  const PermissionGuard = ({ permission, children, fallback = null }) => {
    if (loading) return null; // Don't render anything while loading
    return hasPermission(permission) ? children : fallback;
  };

  const AnyPermissionGuard = ({ permissions: requiredPermissions, children, fallback = null }) => {
    if (loading) return null;
    return hasAnyPermission(requiredPermissions) ? children : fallback;
  };

  const AllPermissionGuard = ({ permissions: requiredPermissions, children, fallback = null }) => {
    if (loading) return null;
    return hasAllPermissions(requiredPermissions) ? children : fallback;
  };

  // Navigation guards - check if user can access specific routes
  const canAccessRoute = (routePermissions) => {
    if (!routePermissions || routePermissions.length === 0) return true;
    return hasAnyPermission(routePermissions);
  };

  return {
    // Permission state
    permissions,
    loading,
    
    // Basic permission checkers
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    
    // Specific permission checkers
    canCreatePost,
    canAccessMessages,
    canViewAnalytics,
    canManageUsers,
    canManageBrands,
    canBlockUnblock,
    canAccessFeedback,
    
    // Combined permission checkers
    canAccessContent,
    canAccessAdminFeatures,
    isFullAdmin,
    
    // Render guards
    PermissionGuard,
    AnyPermissionGuard,
    AllPermissionGuard,
    
    // Navigation guards
    canAccessRoute,
  };
};
