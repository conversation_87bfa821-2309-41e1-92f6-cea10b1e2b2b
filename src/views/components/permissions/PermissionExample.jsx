import React from 'react';
import { <PERSON>, Typo<PERSON>, <PERSON>ton, Card, CardContent, Grid } from '@mui/material';
import { usePermissionGuard } from '../../../helpers/hooks/usePermissionGuard';
import { PERMISSION_KEYS } from '../../../helpers/constant/permissions';
import PermissionButton from '../../../helpers/components/PermissionButton';
import PermissionDisplay from '../../../helpers/components/PermissionDisplay';

/**
 * PermissionExample Component
 * 
 * Demonstrates how to use the permission system in your components
 */
const PermissionExample = () => {
  const {
    permissions,
    loading,
    canCreatePost,
    canAccessMessages,
    canViewAnalytics,
    canManageUsers,
    PermissionGuard,
    AnyPermissionGuard,
  } = usePermissionGuard();

  if (loading) {
    return (
      <Box p={3}>
        <Typography>Loading permissions...</Typography>
      </Box>
    );
  }

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Permission System Examples
      </Typography>

      <Grid container spacing={3}>
        {/* Permission Display */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Current Permissions
              </Typography>
              <PermissionDisplay variant="chips" />
            </CardContent>
          </Card>
        </Grid>

        {/* Permission Guards */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Permission Guards
              </Typography>
              
              <Box mb={2}>
                <Typography variant="subtitle2" gutterBottom>
                  Single Permission Guard (Post Permission):
                </Typography>
                <PermissionGuard 
                  permission={PERMISSION_KEYS.PRM_POST}
                  fallback={<Typography color="text.secondary">You don't have post permissions</Typography>}
                >
                  <Typography color="success.main">✓ You can create posts!</Typography>
                </PermissionGuard>
              </Box>

              <Box mb={2}>
                <Typography variant="subtitle2" gutterBottom>
                  Any Permission Guard (Post OR Message):
                </Typography>
                <AnyPermissionGuard 
                  permissions={[PERMISSION_KEYS.PRM_POST, PERMISSION_KEYS.PRM_MESSAGE]}
                  fallback={<Typography color="text.secondary">You need post or message permissions</Typography>}
                >
                  <Typography color="success.main">✓ You have content permissions!</Typography>
                </AnyPermissionGuard>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Permission Buttons */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Permission Buttons
              </Typography>
              
              <Box display="flex" flexDirection="column" gap={2}>
                <PermissionButton
                  permission={PERMISSION_KEYS.PRM_POST}
                  variant="contained"
                  color="primary"
                >
                  Create Post
                </PermissionButton>

                <PermissionButton
                  permission={PERMISSION_KEYS.PRM_MESSAGE}
                  variant="outlined"
                  color="secondary"
                >
                  Send Message
                </PermissionButton>

                <PermissionButton
                  permission={PERMISSION_KEYS.PRM_ANALYTICS}
                  variant="contained"
                  color="info"
                  hideWhenDisabled={true}
                >
                  View Analytics (Hidden if no permission)
                </PermissionButton>

                <PermissionButton
                  permissions={[PERMISSION_KEYS.PRM_USER_MANAGEMENT, PERMISSION_KEYS.PRM_BRAND_MANAGEMENT]}
                  requireAll={false}
                  variant="contained"
                  color="warning"
                >
                  Admin Actions (Any Admin Permission)
                </PermissionButton>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Conditional Rendering Examples */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Conditional Rendering Examples
              </Typography>
              
              <Box display="flex" flexWrap="wrap" gap={2}>
                {canCreatePost() && (
                  <Button variant="contained" color="primary">
                    Create New Post
                  </Button>
                )}

                {canAccessMessages() && (
                  <Button variant="contained" color="secondary">
                    Open Messages
                  </Button>
                )}

                {canViewAnalytics() && (
                  <Button variant="contained" color="info">
                    View Analytics
                  </Button>
                )}

                {canManageUsers() && (
                  <Button variant="contained" color="warning">
                    Manage Users
                  </Button>
                )}
              </Box>

              {!canCreatePost() && !canAccessMessages() && !canViewAnalytics() && !canManageUsers() && (
                <Typography color="text.secondary" mt={2}>
                  You don't have permissions for any of these actions.
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Raw Permissions Data */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Raw Permissions Data
              </Typography>
              <Box 
                component="pre" 
                sx={{ 
                  backgroundColor: 'grey.100', 
                  p: 2, 
                  borderRadius: 1,
                  overflow: 'auto',
                  fontSize: '0.875rem'
                }}
              >
                {JSON.stringify(permissions, null, 2)}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default PermissionExample;
