Future<void> _handleUserPermissions(String loginUserId, String brandId, String currentUserId) async {
    try {
      Logger.lOG("=== PERMISSION HANDLING DEBUG ===");
      Logger.lOG("Login User ID: $loginUserId");
      Logger.lOG("Current User ID: $currentUserId");
      Logger.lOG("Brand ID: $brandId");
      Logger.lOG("Are they same user? ${loginUserId == currentUserId}");
      // Check if login user is switching back to their own account
      if (loginUserId == currentUserId) {
        Logger.lOG("Login user switching back to own account - granting all permissions");
        // Grant all permissions when login user switches back to their own account
        await _storeAllPermissions(true);
      } else {
        Logger.lOG(":arrows_counterclockwise: User switching to different account - fetching permissions from API");
        // Fetch permissions from API for other users
        final currentUserRole = await apiClient.getUserPermissions(
          logInUserId: loginUserId,
          brandid: brandId,
        );

        Logger.lOG("API Response Status: ${currentUserRole.status}");
        Logger.lOG("API Response Data: ${currentUserRole.data}");
        Logger.lOG("API Response Message: ${currentUserRole.message}");
        // ignore: unnecessary_null_comparison
        if (currentUserRole.status == true && currentUserRole.data != null) {
          await _storePermissions(currentUserRole.data);
          Logger.lOG("Permissions stored successfully for user: $currentUserId");
        } else {
          // If API fails, set all permissions to false
          await _storeAllPermissions(false);
          Logger.lOG(":x: API failed - setting all permissions to false");
        }
      }
      await _printCurrentPermissions();
    } catch (error) {
      Logger.lOG("Error fetching user permissions: $error");
      // On error, set all permissions to false for security
      await _storeAllPermissions(false);
    }
  }

  /// Store permissions based on API response
  Future<void> _storePermissions(List<int> permissionIds) async {
    // Define permission mapping
    final Map<int, String> permissionMap = {
      1: Prefkeys.PRM_POST,
      2: Prefkeys.PRM_MESSAGE,
      3: Prefkeys.PRM_ANALYTICS,
      4: Prefkeys.PRM_USER_MANGEMENT,
      5: Prefkeys.PRM_BRAND_MANGEMENT,
      6: Prefkeys.PRM_BLOCK_UNBLOCK,
      7: Prefkeys.PRM_FEEDBACK,
    };

    // Prepare batch data for optimized storage
    Map<String, dynamic> permissionData = {};

    // Set all permissions to false first
    for (String permissionKey in permissionMap.values) {
      permissionData[permissionKey] = false;
    }

    // Set permissions to true for IDs present in response
    for (int permissionId in permissionIds) {
      if (permissionMap.containsKey(permissionId)) {
        permissionData[permissionMap[permissionId]!] = true;
      }
    }
    // Special condition: If 8 is present (regardless of 1), or if both 1 and 8 are present, set PRM_POST to true
    if (permissionIds.contains(8)) {
      permissionData[Prefkeys.PRM_POST] = true;
    }
    // Use batch storage for better performance
    await OptimizedStorage.batchPut(permissionData);

    Logger.lOG("Permissions stored: $permissionData");

    isPostPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_POST) ?? false;
    isMassagePermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_MESSAGE) ?? false;
    isanalyticsPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_ANALYTICS) ?? false;
    isUserManagementPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_USER_MANGEMENT) ?? false;
    isBrandManagementPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_BRAND_MANGEMENT) ?? false;
    isBlockUnblockPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_BLOCK_UNBLOCK) ?? false;
    isFeedbackPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_FEEDBACK) ?? false;
  }

  Future<void> _storeAllPermissions(bool value) async {
    final Map<String, dynamic> permissionData = {
      Prefkeys.PRM_POST: value,
      Prefkeys.PRM_MESSAGE: value,
      Prefkeys.PRM_ANALYTICS: value,
      Prefkeys.PRM_USER_MANGEMENT: value,
      Prefkeys.PRM_BRAND_MANGEMENT: value,
      Prefkeys.PRM_BLOCK_UNBLOCK: value,
      Prefkeys.PRM_FEEDBACK: value,
    };

    await OptimizedStorage.batchPut(permissionData);
    Logger.lOG("All permissions set to: $value");
    isPostPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_POST) ?? false;
    isMassagePermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_MESSAGE) ?? false;
    isanalyticsPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_ANALYTICS) ?? false;
    isUserManagementPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_USER_MANGEMENT) ?? false;
    isBrandManagementPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_BRAND_MANGEMENT) ?? false;
    isBlockUnblockPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_BLOCK_UNBLOCK) ?? false;
    isFeedbackPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_FEEDBACK) ?? false;
  }

  Future<void> _printCurrentPermissions() async {
    Logger.lOG("=== CURRENT PERMISSIONS IN STORAGE ===");
    Logger.lOG("PRM_POST: ${Prefobj.preferences?.get(Prefkeys.PRM_POST)}");
    Logger.lOG("PRM_MESSAGE: ${Prefobj.preferences?.get(Prefkeys.PRM_MESSAGE)}");
    Logger.lOG("PRM_ANALYTICS: ${Prefobj.preferences?.get(Prefkeys.PRM_ANALYTICS)}");
    Logger.lOG("PRM_USER_MANGEMENT: ${Prefobj.preferences?.get(Prefkeys.PRM_USER_MANGEMENT)}");
    Logger.lOG("PRM_BRAND_MANGEMENT: ${Prefobj.preferences?.get(Prefkeys.PRM_BRAND_MANGEMENT)}");
    Logger.lOG("PRM_BLOCK_UNBLOCK: ${Prefobj.preferences?.get(Prefkeys.PRM_BLOCK_UNBLOCK)}");
    Logger.lOG("PRM_FEEDBACK: ${Prefobj.preferences?.get(Prefkeys.PRM_FEEDBACK)}");
    Logger.lOG("=== END PERMISSIONS ===");
  }